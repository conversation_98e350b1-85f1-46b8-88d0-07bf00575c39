# Use Node.js 18 Alpine for smaller size and faster builds
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for Puppeteer and Chrome
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    curl \
    && rm -rf /var/cache/apk/*

# Set environment variables for npm optimization
ENV NPM_CONFIG_PRODUCTION=true
ENV NPM_CONFIG_CACHE=/tmp/.npm
ENV NPM_CONFIG_FUND=false
ENV NPM_CONFIG_AUDIT=false
ENV NPM_CONFIG_UPDATE_NOTIFIER=false
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install Node.js dependencies with optimizations
RUN npm ci --only=production --no-audit --no-fund --prefer-offline --progress=false \
    && npm cache clean --force \
    && rm -rf /tmp/.npm

# Copy application code
COPY . .

# Create directory for WhatsApp sessions with proper permissions
RUN mkdir -p /app/.wwebjs_auth && chmod 755 /app/.wwebjs_auth

# Set final environment variables
ENV NODE_ENV=production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 && \
    chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3002/api/status || exit 1

# Start the application
CMD ["npm", "start"]
