#!/bin/bash

# 🚀 Railway Deployment Script for WhatsApp Chatbot
# This script helps you deploy your WhatsApp bot to Railway

set -e

echo "🚀 WhatsApp Chatbot - Railway Deployment"
echo "========================================"

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "📁 Initializing Git repository..."
    git init
    git add .
    git commit -m "Initial commit - WhatsApp Chatbot for Railway"
fi

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "📦 Railway CLI not found. Installing..."
    echo "Please install Railway CLI first:"
    echo "npm install -g @railway/cli"
    echo "or visit: https://docs.railway.app/develop/cli"
    exit 1
fi

# Login to Railway
echo "🔐 Logging into Railway..."
railway login

# Create new project or link existing
echo "🚀 Setting up Railway project..."
if [ ! -f "railway.json" ]; then
    railway init
else
    echo "✅ Railway project already configured"
fi

# Set environment variables
echo "🔧 Setting up environment variables..."
echo ""
echo "Please provide the following environment variables:"

read -p "Enter your Google Gemini API Key: " API_KEY
read -p "Enter your Google Sheets ID: " SPREADSHEET_ID

# Set variables in Railway
railway variables set API_KEY="$API_KEY"
railway variables set SPREADSHEET_ID="$SPREADSHEET_ID"
railway variables set NODE_ENV="production"
railway variables set PORT="3002"

echo "✅ Environment variables set successfully!"

# Handle Google Credentials
echo ""
echo "📋 Google Sheets Credentials Setup:"
echo "1. Place your credentials.json file in this directory"
echo "2. Or provide base64 encoded credentials"
echo ""

if [ -f "credentials.json" ]; then
    echo "📁 Found credentials.json file"
    echo "🔄 Converting to base64..."
    
    if command -v base64 &> /dev/null; then
        CREDENTIALS_BASE64=$(base64 -w 0 credentials.json)
        railway variables set GOOGLE_CREDENTIALS_BASE64="$CREDENTIALS_BASE64"
        echo "✅ Google credentials uploaded successfully!"
    else
        echo "⚠️ base64 command not found. Please manually convert credentials.json to base64"
        echo "and set GOOGLE_CREDENTIALS_BASE64 environment variable in Railway dashboard"
    fi
else
    echo "⚠️ credentials.json not found in current directory"
    echo "Please either:"
    echo "1. Place credentials.json in this directory and run script again"
    echo "2. Manually set GOOGLE_CREDENTIALS_BASE64 in Railway dashboard"
fi

# Deploy to Railway
echo ""
echo "🚀 Deploying to Railway..."
git add .
git commit -m "Deploy WhatsApp Chatbot to Railway" || echo "No changes to commit"

railway up

echo ""
echo "✅ Deployment initiated!"
echo "🌐 Your WhatsApp bot is being deployed to Railway..."
echo ""
echo "📋 Next steps:"
echo "1. Wait for deployment to complete"
echo "2. Visit your Railway dashboard to get the app URL"
echo "3. Open the URL to see your REAL WhatsApp QR code"
echo "4. Scan the QR code with WhatsApp to connect"
echo "5. Start chatting with your bot!"
echo ""
echo "🔧 Useful commands:"
echo "railway logs          # View deployment logs"
echo "railway open          # Open app in browser"
echo "railway status        # Check deployment status"
echo "railway variables     # Manage environment variables"
echo ""
echo "🎉 Your WhatsApp bot will have REAL functionality on Railway!"
