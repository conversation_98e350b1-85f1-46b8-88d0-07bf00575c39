const QRCode = require('qrcode');

// Generate a demo QR code with clear messaging that this is NOT real
async function generateDemoQR() {
  try {
    // Generate a clear demo message
    const demoMessage = `🚨 DEMO ONLY - NOT REAL WHATSAPP QR CODE 🚨

This is a demonstration deployment on Netlify.

❌ This QR code will NOT connect to WhatsApp
❌ This is NOT a real WhatsApp bot

✅ For REAL WhatsApp functionality:
1. Download the source code
2. Run locally: npm start
3. Scan the REAL QR code generated

Repository: https://github.com/your-repo
Local URL: http://localhost:3002

BPS Pontianak Chatbot - Demo Version`;

    return await QRCode.toDataURL(demoMessage, {
      width: 400,
      margin: 3,
      color: {
        dark: '#FF0000',  // Red color to indicate demo
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    });
  } catch (error) {
    console.error('Error generating demo QR:', error);
    return null;
  }
}

exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/api', '');

  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    if (path === '/status' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          environment: 'netlify',
          clientReady: false,
          hasQRCode: true,
          isDemo: true,
          message: '🚨 DEMO DEPLOYMENT - NOT REAL WHATSAPP BOT 🚨',
          instructions: 'For real WhatsApp functionality, run locally with: npm start',
          localUrl: 'http://localhost:3002'
        })
      };
    }

    if (path === '/qr' && event.httpMethod === 'GET') {
      const qrCode = await generateDemoQR();

      if (qrCode) {
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            qrCode: qrCode,
            demo: true,
            isReal: false,
            message: '🚨 DEMO QR CODE - WILL NOT CONNECT TO WHATSAPP 🚨',
            instructions: 'Scan this QR code to see the demo message. For REAL WhatsApp bot, run locally.',
            realBotInstructions: {
              step1: 'Download source code from repository',
              step2: 'Run: npm install',
              step3: 'Run: npm start',
              step4: 'Visit: http://localhost:3002',
              step5: 'Scan the REAL QR code generated'
            }
          })
        };
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: "Failed to generate demo QR code" })
        };
      }
    }

    if (path === '/health' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          path: path,
          method: event.httpMethod
        })
      };
    }

    // Default response for unknown paths
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({
        error: 'Not found',
        path: path,
        availableEndpoints: ['/status', '/qr', '/health']
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
