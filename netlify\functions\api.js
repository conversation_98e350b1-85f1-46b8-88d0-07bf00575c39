const QRCode = require('qrcode');

// ⚠️ ATTEMPTING TO LOAD WHATSAPP WEB.JS ON NETLIFY
// This will fail because Netlify doesn't support Puppeteer/Chrome
let Client, LocalAuth;
let whatsappAvailable = false;

try {
  // Try to load WhatsApp Web.js
  const whatsappWebJs = require('whatsapp-web.js');
  Client = whatsappWebJs.Client;
  LocalAuth = whatsappWebJs.LocalAuth;
  whatsappAvailable = true;
  console.log('✅ WhatsApp Web.js loaded successfully');
} catch (error) {
  console.error('❌ WhatsApp Web.js not available on Netlify:', error.message);
  whatsappAvailable = false;
}

// Attempt to generate REAL WhatsApp QR code (will fail on Netlify)
async function attemptRealWhatsAppQR() {
  if (!whatsappAvailable) {
    throw new Error('WhatsApp Web.js not available - missing Puppeteer/Chrome');
  }

  try {
    console.log('🔄 Attempting to create WhatsApp client on Netlify...');

    const client = new Client({
      authStrategy: new LocalAuth({
        dataPath: '/tmp/.wwebjs_auth_netlify'
      }),
      puppeteer: {
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      }
    });

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timeout: WhatsApp QR generation took too long (Netlify 10s limit)'));
      }, 8000); // 8 seconds (before Netlify 10s timeout)

      client.on('qr', async (qr) => {
        clearTimeout(timeout);
        console.log('✅ REAL WhatsApp QR received!');

        try {
          const qrCodeImage = await QRCode.toDataURL(qr, {
            width: 300,
            margin: 2,
            errorCorrectionLevel: 'M'
          });
          resolve({
            qrCode: qrCodeImage,
            isReal: true,
            message: '✅ REAL WhatsApp QR Code Generated!'
          });
        } catch (error) {
          reject(error);
        }
      });

      client.on('auth_failure', (msg) => {
        clearTimeout(timeout);
        reject(new Error(`WhatsApp auth failure: ${msg}`));
      });

      client.initialize().catch(reject);
    });

  } catch (error) {
    throw new Error(`WhatsApp client creation failed: ${error.message}`);
  }
}

// Generate a demo QR code with clear messaging that this is NOT real
async function generateDemoQR() {
  try {
    // Generate a clear demo message
    const demoMessage = `🚨 DEMO ONLY - NOT REAL WHATSAPP QR CODE 🚨

This is a demonstration deployment on Netlify.

❌ This QR code will NOT connect to WhatsApp
❌ This is NOT a real WhatsApp bot

✅ For REAL WhatsApp functionality:
1. Download the source code
2. Run locally: npm start
3. Scan the REAL QR code generated

Repository: https://github.com/your-repo
Local URL: http://localhost:3002

BPS Pontianak Chatbot - Demo Version`;

    return await QRCode.toDataURL(demoMessage, {
      width: 400,
      margin: 3,
      color: {
        dark: '#FF0000',  // Red color to indicate demo
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    });
  } catch (error) {
    console.error('Error generating demo QR:', error);
    return null;
  }
}

exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/api', '');

  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    if (path === '/status' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          environment: 'netlify',
          clientReady: false,
          hasQRCode: true,
          isDemo: true,
          message: '🚨 DEMO DEPLOYMENT - NOT REAL WHATSAPP BOT 🚨',
          instructions: 'For real WhatsApp functionality, run locally with: npm start',
          localUrl: 'http://localhost:3002'
        })
      };
    }

    if (path === '/qr' && event.httpMethod === 'GET') {
      // Check if user wants to force attempt real WhatsApp QR
      const forceReal = event.queryStringParameters?.real === 'true';

      if (forceReal) {
        console.log('🔄 User requested REAL WhatsApp QR - attempting...');

        try {
          const realQR = await attemptRealWhatsAppQR();
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
              qrCode: realQR.qrCode,
              demo: false,
              isReal: true,
              message: realQR.message,
              success: true,
              note: 'This is a REAL WhatsApp QR code generated on Netlify!'
            })
          };
        } catch (error) {
          console.error('❌ Real WhatsApp QR failed as expected:', error.message);

          // Return detailed error explanation
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
              qrCode: null,
              demo: false,
              isReal: false,
              success: false,
              error: error.message,
              message: '❌ REAL WhatsApp QR Failed on Netlify (Expected)',
              explanation: {
                reason: 'Netlify serverless functions cannot run WhatsApp Web.js',
                technical: [
                  'Missing Puppeteer/Chrome browser',
                  'No persistent file system',
                  '10-second function timeout',
                  'No WebSocket support for persistent connections'
                ],
                solution: 'Use VPS deployment or run locally for real WhatsApp functionality'
              },
              fallbackToDemo: true
            })
          };
        }
      }

      // Default: Generate demo QR
      const qrCode = await generateDemoQR();

      if (qrCode) {
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            qrCode: qrCode,
            demo: true,
            isReal: false,
            message: '🚨 DEMO QR CODE - WILL NOT CONNECT TO WHATSAPP 🚨',
            instructions: 'Scan this QR code to see the demo message. For REAL WhatsApp bot, run locally.',
            realBotInstructions: {
              step1: 'Download source code from repository',
              step2: 'Run: npm install',
              step3: 'Run: npm start',
              step4: 'Visit: http://localhost:3002',
              step5: 'Scan the REAL QR code generated'
            },
            tryRealQR: 'Add ?real=true to this URL to attempt real WhatsApp QR (will fail)'
          })
        };
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: "Failed to generate demo QR code" })
        };
      }
    }

    if (path === '/health' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          path: path,
          method: event.httpMethod
        })
      };
    }

    // Default response for unknown paths
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({
        error: 'Not found',
        path: path,
        availableEndpoints: ['/status', '/qr', '/health']
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
