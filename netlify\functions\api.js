const QRCode = require('qrcode');

// Generate a demo QR code for display purposes
async function generateDemoQR() {
  try {
    // Generate a more realistic WhatsApp-like QR code format
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const demoWhatsAppData = `1@${randomId},${timestamp},netlify-demo,BPS-Pontianak-Chatbot`;

    return await QRCode.toDataURL(demoWhatsAppData, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    });
  } catch (error) {
    console.error('Error generating demo QR:', error);
    return null;
  }
}

exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/api', '');

  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    if (path === '/status' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          environment: 'netlify',
          clientReady: false,
          hasQRCode: true,
          message: 'This is a demo deployment. For actual WhatsApp functionality, run locally.'
        })
      };
    }

    if (path === '/qr' && event.httpMethod === 'GET') {
      const qrCode = await generateDemoQR();

      if (qrCode) {
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            qrCode: qrCode,
            demo: true,
            message: 'This is a demo QR code. For actual WhatsApp connection, run the bot locally.'
          })
        };
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: "Failed to generate demo QR code" })
        };
      }
    }

    if (path === '/health' && event.httpMethod === 'GET') {
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          status: 'ok',
          timestamp: new Date().toISOString(),
          path: path,
          method: event.httpMethod
        })
      };
    }

    // Default response for unknown paths
    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({
        error: 'Not found',
        path: path,
        availableEndpoints: ['/status', '/qr', '/health']
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
