# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# WhatsApp session data (created at runtime)
.wwebjs_auth/
.wwebjs_cache/

# Git
.git/
.gitignore

# Documentation (not needed for build)
*.md
docs/
DEPLOYMENT-GUIDE.md
RAILWAY-DEPLOYMENT.md
BUILD-OPTIMIZATION.md
RAILWAY-FIX.md
MIGRATION-SUMMARY.md
REAL-QR-EXPLANATION.md

# Test files
test/
tests/
*.test.js
*.spec.js
test-*.js

# Development files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# QR code files (generated at runtime)
qr.png
*.png

# Temporary files
tmp/
temp/

# Deployment scripts (not needed in production)
deploy-*.sh
setup-*.sh

# Netlify remnants (not needed for Railway)
netlify.toml
.netlify/
public/

# Development tools
nixpacks.toml
