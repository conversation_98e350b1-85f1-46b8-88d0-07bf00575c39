// Test script to verify Railway deployment readiness
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Railway Deployment Readiness...\n');

// Test 1: Check required files
console.log('📁 Checking required files...');
const requiredFiles = [
  'index.js',
  'package.json',
  'railway.json',
  'nixpacks.toml',
  'Procfile'
];

let filesOk = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - Found`);
  } else {
    console.log(`❌ ${file} - Missing`);
    filesOk = false;
  }
});

// Test 2: Check package.json
console.log('\n📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

  // Check dependencies
  const requiredDeps = ['whatsapp-web.js', 'puppeteer', 'express', '@google/generative-ai'];
  let depsOk = true;

  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep} - Found in dependencies`);
    } else {
      console.log(`❌ ${dep} - Missing from dependencies`);
      depsOk = false;
    }
  });

  // Check scripts
  if (packageJson.scripts && packageJson.scripts.start) {
    console.log(`✅ start script - Found: ${packageJson.scripts.start}`);
  } else {
    console.log(`❌ start script - Missing`);
    depsOk = false;
  }

  if (!depsOk) filesOk = false;

} catch (error) {
  console.log(`❌ package.json - Invalid JSON: ${error.message}`);
  filesOk = false;
}

// Test 3: Check environment variables setup
console.log('\n🔧 Checking environment variables...');
const requiredEnvVars = ['API_KEY', 'SPREADSHEET_ID'];
let envOk = true;

requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} - Set`);
  } else {
    console.log(`⚠️ ${envVar} - Not set (required for Railway)`);
    // Don't fail for env vars as they'll be set in Railway
  }
});

// Test 4: Check Google credentials
console.log('\n📋 Checking Google credentials...');
if (fs.existsSync('credentials.json')) {
  console.log('✅ credentials.json - Found (will be converted to base64 for Railway)');
} else if (process.env.GOOGLE_CREDENTIALS_BASE64) {
  console.log('✅ GOOGLE_CREDENTIALS_BASE64 - Set');
} else {
  console.log('⚠️ Google credentials - Not found (add credentials.json or set GOOGLE_CREDENTIALS_BASE64)');
}

// Test 5: Test basic Express server
console.log('\n🌐 Testing Express server setup...');
try {
  // Don't actually start the server, just check if it can be required
  const express = require('express');
  console.log('✅ Express - Can be loaded');

  // Check if main file can be parsed
  const indexContent = fs.readFileSync('index.js', 'utf8');
  if (indexContent.includes('app.listen')) {
    console.log('✅ Server setup - Found app.listen()');
  } else {
    console.log('❌ Server setup - app.listen() not found');
    filesOk = false;
  }

} catch (error) {
  console.log(`❌ Express setup - Error: ${error.message}`);
  filesOk = false;
}

// Test 6: Check Railway-specific configurations
console.log('\n🚀 Checking Railway configurations...');
try {
  const railwayConfig = JSON.parse(fs.readFileSync('railway.json', 'utf8'));
  if (railwayConfig.build && railwayConfig.build.builder) {
    console.log(`✅ Railway builder - ${railwayConfig.build.builder}`);
  }
  if (railwayConfig.deploy && railwayConfig.deploy.startCommand) {
    console.log(`✅ Railway start command - ${railwayConfig.deploy.startCommand}`);
  } else {
    console.log('⚠️ Railway start command - Not configured');
  }
} catch (error) {
  console.log(`❌ Railway config - Error: ${error.message}`);
}

// Test 7: Check Dockerfile
console.log('\n🐳 Checking Dockerfile...');
if (fs.existsSync('Dockerfile')) {
  console.log('✅ Dockerfile - Found');
  try {
    const dockerfileContent = fs.readFileSync('Dockerfile', 'utf8');
    if (dockerfileContent.includes('google-chrome-stable')) {
      console.log('✅ Chrome installation - Configured in Dockerfile');
    } else {
      console.log('⚠️ Chrome installation - Not found in Dockerfile');
    }
    if (dockerfileContent.includes('PUPPETEER_EXECUTABLE_PATH')) {
      console.log('✅ Puppeteer configuration - Found in Dockerfile');
    } else {
      console.log('⚠️ Puppeteer configuration - Not found in Dockerfile');
    }
  } catch (error) {
    console.log(`❌ Dockerfile reading - Error: ${error.message}`);
  }
} else {
  console.log('❌ Dockerfile - Missing (required for Railway)');
  filesOk = false;
}

// Test 8: Check for Netlify remnants
console.log('\n🧹 Checking for Netlify remnants...');
const netlifyFiles = ['netlify.toml', 'netlify/functions/api.js', 'public/index.html'];
let netlifyFound = false;

netlifyFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`⚠️ ${file} - Netlify file found (should be removed)`);
    netlifyFound = true;
  }
});

if (!netlifyFound) {
  console.log('✅ No Netlify files found - Clean Railway setup');
}

// Final result
console.log('\n' + '='.repeat(50));
if (filesOk && !netlifyFound) {
  console.log('🎉 RAILWAY DEPLOYMENT READY!');
  console.log('\n📋 Next steps:');
  console.log('1. Run: chmod +x deploy-railway.sh');
  console.log('2. Run: ./deploy-railway.sh');
  console.log('3. Or manually deploy to Railway');
  console.log('\n🚀 Your WhatsApp bot will have REAL functionality on Railway!');
} else {
  console.log('❌ DEPLOYMENT NOT READY');
  console.log('\n🔧 Please fix the issues above before deploying to Railway');

  if (netlifyFound) {
    console.log('\n🧹 Remove Netlify files:');
    netlifyFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`rm -rf ${file}`);
      }
    });
  }
}

console.log('\n📚 For detailed instructions, see RAILWAY-DEPLOYMENT.md');
