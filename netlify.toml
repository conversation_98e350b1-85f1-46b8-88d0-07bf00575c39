[build]
  publish = "public"
  command = "npm run build"

[functions]
  directory = "netlify/functions"

# API function redirects
[[redirects]]
  from = "/.netlify/functions/api/status"
  to = "/.netlify/functions/api"
  status = 200
  force = true

[[redirects]]
  from = "/.netlify/functions/api/qr"
  to = "/.netlify/functions/api"
  status = 200
  force = true

[[redirects]]
  from = "/.netlify/functions/api/health"
  to = "/.netlify/functions/api"
  status = 200
  force = true

# Fallback for SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  NODE_ENV = "production"
  NETLIFY = "true"

# Headers for security and CORS
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type"
    Access-Control-Allow-Methods = "GET, POST, OPTIONS"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
