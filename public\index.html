<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Chatbot BPS Pontianak - Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #FFC107;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border-left-color: #2196F3;
        }
        .demo-section {
            background: rgba(156, 39, 176, 0.2);
            border-left-color: #9C27B0;
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #9C27B0;
        }
        #qrcode {
            max-width: 300px;
            height: auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: white;
            padding: 20px;
            margin: 20px auto;
            display: block;
        }
        .qr-placeholder {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            margin: 20px auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            max-width: 300px;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>

        <!-- DEMO WARNING -->
        <div class="step warning">
            <h2>🚨 DEMO DEPLOYMENT - NOT REAL WHATSAPP BOT 🚨</h2>
            <p><strong>This is a demonstration deployment on Netlify.</strong></p>
            <p>❌ The QR code below will NOT connect to WhatsApp</p>
            <p>❌ This is NOT a functional WhatsApp bot</p>
        </div>

        <!-- REAL BOT INSTRUCTIONS -->
        <div class="step info">
            <h3>✅ For REAL WhatsApp Bot Functionality:</h3>
            <ol>
                <li><strong>Download the source code</strong> from the repository</li>
                <li><strong>Install dependencies:</strong> <code class="code">npm install</code></li>
                <li><strong>Start the bot:</strong> <code class="code">npm start</code></li>
                <li><strong>Visit:</strong> <code class="code">http://localhost:3002</code></li>
                <li><strong>Scan the REAL QR code</strong> generated locally</li>
            </ol>
        </div>

        <!-- DEMO QR SECTION -->
        <div class="demo-section">
            <h3>📱 Demo QR Code (NOT FUNCTIONAL)</h3>
            <p style="color: #ff6b6b; font-weight: bold;">⚠️ This QR code is for demonstration only</p>

            <img id="qrcode" src="" alt="Demo QR Code" style="display: none;" />
            <div id="qr-loading" class="qr-placeholder">⏳ Loading demo QR code...</div>
            <button onclick="loadDemoQR()">🔄 Load Demo QR Code</button>
            <button onclick="attemptRealQR()" style="background: rgba(255, 0, 0, 0.3); border-color: #ff0000;">⚠️ Try REAL QR (Will Fail)</button>

            <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                <p>📋 What happens when you scan this:</p>
                <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
                    <li>You'll see a demo message explaining this is not real</li>
                    <li>It will NOT connect to WhatsApp</li>
                    <li>It will NOT create a working chatbot</li>
                    <li>For real functionality, run the bot locally</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function loadDemoQR() {
            const qrImg = document.getElementById('qrcode');
            const qrLoading = document.getElementById('qr-loading');

            qrLoading.innerHTML = '⏳ Loading QR code...';
            qrImg.style.display = 'none';

            fetch('/.netlify/functions/api/qr')
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('QR data received:', data);
                    if (data.qrCode) {
                        qrImg.src = data.qrCode;
                        qrImg.style.display = 'block';
                        qrLoading.style.display = 'none';

                        if (data.demo) {
                            qrLoading.innerHTML = `
                                <div style="background: #ffebee; color: #c62828; padding: 15px; border-radius: 10px; margin-top: 15px;">
                                    <strong>🚨 ${data.message}</strong><br>
                                    <small>${data.instructions}</small>
                                </div>
                            `;
                            qrLoading.style.display = 'block';
                            qrLoading.style.marginTop = '10px';
                            qrLoading.style.fontSize = '12px';
                        }
                    } else {
                        throw new Error('No QR code in response');
                    }
                })
                .catch(error => {
                    console.error('Error loading demo QR:', error);
                    qrLoading.innerHTML = `❌ Failed to load QR code<br><small>${error.message}</small>`;
                    qrLoading.style.display = 'block';
                    qrImg.style.display = 'none';
                });
        }

        function checkStatus() {
            fetch('/.netlify/functions/api/status')
                .then(response => response.json())
                .then(data => {
                    console.log('Status:', data);
                    const statusInfo = document.createElement('div');
                    statusInfo.style.marginTop = '20px';
                    statusInfo.style.fontSize = '14px';
                    statusInfo.style.opacity = '0.8';
                    statusInfo.innerHTML = `
                        <p>🌐 Environment: ${data.environment}</p>
                        <p>📱 Status: ${data.message}</p>
                    `;

                    // Add status info if not already present
                    if (!document.getElementById('status-info')) {
                        statusInfo.id = 'status-info';
                        document.querySelector('.demo-section').appendChild(statusInfo);
                    }
                })
                .catch(error => {
                    console.error('Error checking status:', error);
                });
        }

        function attemptRealQR() {
            const qrImg = document.getElementById('qrcode');
            const qrLoading = document.getElementById('qr-loading');

            qrLoading.innerHTML = '🔄 Attempting REAL WhatsApp QR on Netlify... (This will fail)';
            qrImg.style.display = 'none';

            fetch('/.netlify/functions/api/qr?real=true')
                .then(response => {
                    console.log('Real QR attempt response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Real QR attempt data:', data);

                    if (data.success && data.qrCode) {
                        // Miracle! It worked (this won't happen)
                        qrImg.src = data.qrCode;
                        qrImg.style.display = 'block';
                        qrLoading.innerHTML = `
                            <div style="background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 10px; margin-top: 15px;">
                                <strong>🎉 MIRACLE! ${data.message}</strong><br>
                                <small>This shouldn't have worked on Netlify!</small>
                            </div>
                        `;
                        qrLoading.style.display = 'block';
                    } else {
                        // Expected failure
                        qrLoading.innerHTML = `
                            <div style="background: #ffebee; color: #c62828; padding: 15px; border-radius: 10px; margin-top: 15px;">
                                <strong>${data.message}</strong><br>
                                <small><strong>Error:</strong> ${data.error}</small><br><br>
                                <strong>Why it failed:</strong><br>
                                ${data.explanation.technical.map(reason => `• ${reason}`).join('<br>')}
                                <br><br>
                                <strong>Solution:</strong> ${data.explanation.solution}
                            </div>
                        `;
                        qrLoading.style.display = 'block';
                        qrImg.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Real QR attempt error:', error);
                    qrLoading.innerHTML = `
                        <div style="background: #ffebee; color: #c62828; padding: 15px; border-radius: 10px; margin-top: 15px;">
                            <strong>❌ Real QR Attempt Failed (Expected)</strong><br>
                            <small>Error: ${error.message}</small><br><br>
                            This proves that Netlify cannot run real WhatsApp bots!
                        </div>
                    `;
                    qrLoading.style.display = 'block';
                    qrImg.style.display = 'none';
                });
        }

        // Auto-load demo QR on page load
        window.addEventListener('load', () => {
            loadDemoQR();
            checkStatus();
        });
    </script>
</body>
</html>
