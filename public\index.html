<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Chatbot BPS Pontianak - Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        .code {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #FFC107;
        }
        .info {
            background: rgba(33, 150, 243, 0.2);
            border-left-color: #2196F3;
        }
        .demo-section {
            background: rgba(156, 39, 176, 0.2);
            border-left-color: #9C27B0;
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #9C27B0;
        }
        #qrcode {
            max-width: 300px;
            height: auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: white;
            padding: 20px;
            margin: 20px auto;
            display: block;
        }
        .qr-placeholder {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            margin: 20px auto;
            border: 2px solid rgba(255, 255, 255, 0.3);
            max-width: 300px;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
        <h2>📱 Scan QR Code with WhatsApp</h2>

        <div class="demo-section">
            <img id="qrcode" src="" alt="QR Code" style="display: none;" />
            <div id="qr-loading" class="qr-placeholder">⏳ Loading QR code...</div>
            <button onclick="loadDemoQR()">🔄 Refresh QR Code</button>

            <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                <p>📋 Instructions:</p>
                <ol style="text-align: left; max-width: 300px; margin: 0 auto;">
                    <li>Open WhatsApp on your phone</li>
                    <li>Go to Settings → Linked Devices</li>
                    <li>Tap "Link a Device"</li>
                    <li>Scan the QR code above</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function loadDemoQR() {
            const qrImg = document.getElementById('qrcode');
            const qrLoading = document.getElementById('qr-loading');

            qrLoading.innerHTML = '⏳ Loading QR code...';
            qrImg.style.display = 'none';

            fetch('/.netlify/functions/api/qr')
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('QR data received:', data);
                    if (data.qrCode) {
                        qrImg.src = data.qrCode;
                        qrImg.style.display = 'block';
                        qrLoading.style.display = 'none';

                        if (data.demo) {
                            qrLoading.innerHTML = '📱 Demo QR Code - Scan to see demo message';
                            qrLoading.style.display = 'block';
                            qrLoading.style.marginTop = '10px';
                            qrLoading.style.fontSize = '12px';
                            qrLoading.style.opacity = '0.8';
                        }
                    } else {
                        throw new Error('No QR code in response');
                    }
                })
                .catch(error => {
                    console.error('Error loading demo QR:', error);
                    qrLoading.innerHTML = `❌ Failed to load QR code<br><small>${error.message}</small>`;
                    qrLoading.style.display = 'block';
                    qrImg.style.display = 'none';
                });
        }

        function checkStatus() {
            fetch('/.netlify/functions/api/status')
                .then(response => response.json())
                .then(data => {
                    console.log('Status:', data);
                    const statusInfo = document.createElement('div');
                    statusInfo.style.marginTop = '20px';
                    statusInfo.style.fontSize = '14px';
                    statusInfo.style.opacity = '0.8';
                    statusInfo.innerHTML = `
                        <p>🌐 Environment: ${data.environment}</p>
                        <p>📱 Status: ${data.message}</p>
                    `;

                    // Add status info if not already present
                    if (!document.getElementById('status-info')) {
                        statusInfo.id = 'status-info';
                        document.querySelector('.demo-section').appendChild(statusInfo);
                    }
                })
                .catch(error => {
                    console.error('Error checking status:', error);
                });
        }

        // Auto-load demo QR on page load
        window.addEventListener('load', () => {
            loadDemoQR();
            checkStatus();
        });
    </script>
</body>
</html>
