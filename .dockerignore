# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# WhatsApp session data (will be created in container)
.wwebjs_auth
.wwebjs_cache

# Git
.git
.gitignore

# Documentation
*.md
docs/

# Test files
test/
tests/
*.test.js
*.spec.js

# Development files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# QR code files (generated at runtime)
qr.png
*.png

# Temporary files
tmp/
temp/

# Railway/Netlify specific
railway.json
nixpacks.toml
netlify.toml
.netlify/

# Deployment scripts
deploy-*.sh
test-*.js
