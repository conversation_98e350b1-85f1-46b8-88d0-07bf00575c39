// Check the current QR code to see if it's real WhatsApp format
const fs = require('fs');

console.log('🔍 Checking current QR code status...\n');

// Check if qr.png exists
if (fs.existsSync('qr.png')) {
  const stats = fs.statSync('qr.png');
  console.log('✅ qr.png exists');
  console.log('📁 File size:', stats.size, 'bytes');
  console.log('📅 Last modified:', stats.mtime.toISOString());
  
  // Check if it's recent (within last 2 minutes)
  const ageMinutes = (Date.now() - stats.mtime.getTime()) / (1000 * 60);
  console.log('⏰ Age:', Math.round(ageMinutes * 100) / 100, 'minutes');
  
  if (ageMinutes < 2) {
    console.log('🟢 QR code is FRESH (generated recently)');
  } else {
    console.log('🟡 QR code is OLD (may be expired)');
  }
} else {
  console.log('❌ qr.png does not exist');
}

// Check if test QR data exists
if (fs.existsSync('test-qr-data.txt')) {
  const qrData = fs.readFileSync('test-qr-data.txt', 'utf8');
  console.log('\n📋 Last test QR data:');
  console.log('🔍 Length:', qrData.length);
  console.log('🔍 Starts with:', qrData.substring(0, 20));
  console.log('🔍 Format check:');
  console.log('  - Contains @:', qrData.includes('@'));
  console.log('  - Contains ,:', qrData.includes(','));
  console.log('  - Starts with 2@:', qrData.startsWith('2@'));
  
  if (qrData.length > 100 && qrData.startsWith('2@') && qrData.includes(',')) {
    console.log('🎉 This IS a real WhatsApp QR code format!');
  } else {
    console.log('⚠️ This does NOT look like a real WhatsApp QR code');
  }
} else {
  console.log('\n❌ No test QR data found');
}

// Check server status
console.log('\n🌐 Server status:');
console.log('Expected URL: http://localhost:3002');

// Check if there are any auth files
if (fs.existsSync('.wwebjs_auth')) {
  console.log('📁 WhatsApp auth folder exists');
} else {
  console.log('📁 No WhatsApp auth folder (first time setup)');
}

console.log('\n💡 Recommendations:');
console.log('1. Visit http://localhost:3002 in your browser');
console.log('2. Check if QR code appears and looks complex (not simple text)');
console.log('3. Scan immediately when it appears (expires in ~20 seconds)');
console.log('4. If it says "invalid", click refresh and try again');
console.log('5. Real WhatsApp QR codes are dense with many small squares');
