{"name": "WhatsApp Chatbot BPS Pontianak", "description": "A production-ready WhatsApp chatbot with Google Gemini AI and Google Sheets integration. Generates REAL WhatsApp QR codes that actually connect to WhatsApp.", "repository": "https://github.com/your-username/whatsapp-chatbot-bps-6171", "services": [{"name": "whatsapp-bot", "source": {"repo": "your-username/whatsapp-chatbot-bps-6171", "ref": "main"}, "variables": {"API_KEY": {"description": "Google Gemini API Key - Get from https://ai.google.dev/", "type": "string", "required": true}, "SPREADSHEET_ID": {"description": "Google Sheets ID for data storage and RAG context", "type": "string", "required": true}, "NODE_ENV": {"description": "Node.js environment", "type": "string", "default": "production"}, "PORT": {"description": "Server port", "type": "string", "default": "3002"}, "GOOGLE_CREDENTIALS_BASE64": {"description": "Base64 encoded Google Sheets credentials.json file", "type": "string", "required": false}}, "build": {"builder": "NIXPACKS", "buildCommand": "npm install"}, "deploy": {"startCommand": "npm start", "healthcheckPath": "/api/status", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}}], "metadata": {"categories": ["chatbot", "whatsapp", "ai", "automation"], "tags": ["whatsapp", "chatbot", "gemini", "google-sheets", "nodejs", "puppeteer"], "icon": "🤖", "featured": true}, "instructions": {"setup": ["1. Get Google Gemini API key from https://ai.google.dev/", "2. Create Google Sheets document with 'RAG' and 'MESSAGE' sheets", "3. Enable Google Sheets API and download credentials.json", "4. Convert credentials.json to base64 and add to GOOGLE_CREDENTIALS_BASE64", "5. Add your API_KEY and SPREADSHEET_ID to environment variables", "6. Deploy and visit your Railway URL to see REAL WhatsApp QR codes!"], "usage": ["• Visit your Railway app URL to see the WhatsApp QR code interface", "• Scan the QR code with WhatsApp to connect your phone", "• Send messages to your WhatsApp number to chat with the AI bot", "• Bot responses are powered by Google Gemini AI", "• All conversations are logged to Google Sheets", "• Bo<PERSON> retrieves context from Google Sheets for accurate responses"], "features": ["✅ REAL WhatsApp QR codes (not demo)", "✅ Google Gemini AI integration", "✅ Google Sheets data logging", "✅ Real-time RAG (Retrieval Augmented Generation)", "✅ Persistent WhatsApp sessions", "✅ Auto-reconnection on disconnects", "✅ Web interface for QR code scanning", "✅ Production-ready deployment"]}}