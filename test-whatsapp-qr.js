// Test script to verify WhatsApp QR code generation
const { Client, LocalAuth } = require("whatsapp-web.js");
const QRCode = require("qrcode");
const fs = require("fs");

console.log("🧪 Testing WhatsApp QR code generation...\n");

const client = new Client({
  authStrategy: new LocalAuth({
    dataPath: "./.wwebjs_auth_test",
  }),
  puppeteer: {
    headless: true,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
    ],
  },
  webVersionCache: {
    type: "remote",
    remotePath:
      "https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html",
  },
});

let qrReceived = false;

client.on("qr", async (qr) => {
  qrReceived = true;
  console.log("✅ REAL WhatsApp QR code received!");
  console.log("🔍 QR code length:", qr.length);
  console.log("🔍 QR code starts with:", qr.substring(0, 20));
  console.log("🔍 QR code contains '@':", qr.includes("@"));
  console.log("🔍 QR code contains ',':", qr.includes(","));

  // Check if it looks like a real WhatsApp QR code
  const isRealWhatsAppQR = qr.length > 100 && qr.includes("@") && qr.includes(",");
  
  if (isRealWhatsAppQR) {
    console.log("🎉 This appears to be a REAL WhatsApp QR code!");
  } else {
    console.log("⚠️ This does NOT appear to be a real WhatsApp QR code");
  }

  try {
    // Save QR code for inspection
    await QRCode.toFile("test-qr.png", qr, {
      width: 300,
      margin: 2,
      errorCorrectionLevel: "M",
    });
    console.log("💾 QR code saved as test-qr.png");
    
    // Save raw QR data for inspection
    fs.writeFileSync("test-qr-data.txt", qr);
    console.log("💾 Raw QR data saved as test-qr-data.txt");
    
  } catch (error) {
    console.error("❌ Error saving QR code:", error);
  }

  // Exit after first QR code
  setTimeout(() => {
    console.log("\n🏁 Test completed. Exiting...");
    process.exit(0);
  }, 2000);
});

client.on("loading_screen", (percent, message) => {
  console.log(`⏳ Loading: ${percent}% - ${message}`);
});

client.on("ready", () => {
  console.log("✅ WhatsApp client ready!");
  if (!qrReceived) {
    console.log("ℹ️ No QR code was needed (already authenticated)");
    setTimeout(() => process.exit(0), 2000);
  }
});

client.on("auth_failure", (msg) => {
  console.error("❌ Authentication failed:", msg);
  process.exit(1);
});

client.on("disconnected", (reason) => {
  console.log("❌ Disconnected:", reason);
  process.exit(1);
});

// Timeout after 60 seconds
setTimeout(() => {
  console.log("⏰ Test timeout - no QR code received in 60 seconds");
  process.exit(1);
}, 60000);

console.log("🚀 Initializing WhatsApp client...");
client.initialize().catch((error) => {
  console.error("❌ Failed to initialize client:", error);
  process.exit(1);
});
