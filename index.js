const fs = require("fs");
const path = require("path");
const express = require("express");
const QRCode = require("qrcode");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { google } = require("googleapis");
require("dotenv").config();

// Environment detection
const isRailway = process.env.RAILWAY_ENVIRONMENT_NAME !== undefined;
const isDevelopment = process.env.NODE_ENV !== "production" && !isRailway;
const isProduction = process.env.NODE_ENV === "production" || isRailway;

console.log("🔧 Environment Configuration:");
console.log("- NODE_ENV:", process.env.NODE_ENV);
console.log("- RAILWAY_ENVIRONMENT:", process.env.RAILWAY_ENVIRONMENT_NAME);
console.log("- isRailway:", isRailway);
console.log("- isDevelopment:", isDevelopment);
console.log("- isProduction:", isProduction);

// --- Gemini AI Init ---
const genAI = new GoogleGenerativeAI(process.env.API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

// --- Express Server Setup ---
const app = express();
const PORT = process.env.PORT || 3002;

app.use(express.static(path.join(__dirname)));
app.use(express.json());

// Global variables for QR code and client state
let currentQRCode = null;
let clientReady = false;
let client = null;

// Initialize WhatsApp client - REAL QR CODES for Development and Railway
if (isDevelopment || isRailway) {
  try {
    const { Client, LocalAuth } = require("whatsapp-web.js");
    const qrcode = require("qrcode-terminal");

    // Clean up any existing Chrome lock files before creating client
    const fs = require('fs');
    const path = require('path');
    const authPath = isRailway ? "/app/.wwebjs_auth" : "./.wwebjs_auth";
    const lockFile = path.join(authPath, 'session', 'SingletonLock');

    try {
      if (fs.existsSync(lockFile)) {
        console.log("🧹 Cleaning up existing Chrome lock file at startup...");
        fs.unlinkSync(lockFile);
        console.log("✅ Startup lock file removed successfully");
      }
    } catch (error) {
      console.log("⚠️ Could not remove startup lock file:", error.message);
    }

    // --- WhatsApp Client - REAL CONNECTION ONLY ---
    client = new Client({
      authStrategy: new LocalAuth({
        dataPath: authPath,
      }),
      puppeteer: {
        headless: true,
        executablePath: isRailway ? '/usr/bin/chromium-browser' : undefined,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu",
          "--disable-extensions",
          "--disable-background-timer-throttling",
          "--disable-backgrounding-occluded-windows",
          "--disable-renderer-backgrounding",
          "--disable-web-security",
          "--disable-features=VizDisplayCompositor",
          // Fix for Chrome singleton lock issues on Railway
          "--disable-features=TranslateUI",
          "--disable-ipc-flooding-protection",
          "--disable-background-networking",
          "--disable-default-apps",
          "--disable-sync",
          "--disable-translate",
          "--hide-scrollbars",
          "--metrics-recording-only",
          "--mute-audio",
          "--no-default-browser-check",
          "--safebrowsing-disable-auto-update",
          "--disable-crash-reporter",
          "--disable-in-process-stack-traces",
          "--disable-software-rasterizer",
          "--single-process"
        ],
      },
      webVersionCache: {
        type: "remote",
        remotePath:
          "https://raw.githubusercontent.com/wppconnect-team/wa-version/main/html/2.2412.54.html",
      },
    });

    console.log("✅ WhatsApp Web.js client created successfully");
    console.log("✅ Configuration: REAL QR CODES ONLY");
    console.log("🔧 Platform:", isRailway ? "Railway" : "Local");
    console.log("🔧 Data path:", isRailway ? "/app/.wwebjs_auth" : "./.wwebjs_auth");
  } catch (error) {
    console.error("❌ CRITICAL: WhatsApp Web.js not available:", error.message);
    console.log("� Cannot generate REAL QR codes without WhatsApp Web.js");
    console.log("📝 Run: npm install whatsapp-web.js puppeteer");
    process.exit(1); // Exit if can't create real WhatsApp connection
  }
} else {
  console.log("⚠️ WhatsApp client not initialized - not in development or Railway mode");
}

// WhatsApp client event handlers - REAL QR CODES ONLY
if (client) {
  console.log("🔧 Setting up REAL WhatsApp client event handlers...");

  client.on("qr", async (qr) => {
    console.log("📱 REAL WhatsApp QR code received from WhatsApp servers!");
    console.log("🔍 QR code length:", qr.length);
    console.log("🔍 QR code preview:", qr.substring(0, 50) + "...");

    // Display QR code in terminal for Railway logs
    if (isRailway) {
      const qrcode = require("qrcode-terminal");
      console.log("📱 QR Code for Railway (scan this in logs):");
      qrcode.generate(qr, { small: true });
    }

    // Verify this is a real WhatsApp QR code
    const isRealWhatsApp =
      qr.length > 100 &&
      qr.includes("@") &&
      qr.includes(",") &&
      /^[0-9]@/.test(qr);

    if (!isRealWhatsApp) {
      console.error("❌ CRITICAL: Received invalid QR code format!");
      return;
    }

    console.log("✅ Verified: This is a REAL WhatsApp QR code!");

    try {
      // Generate QR code as base64 for web interface
      currentQRCode = await QRCode.toDataURL(qr, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        errorCorrectionLevel: "M",
      });

      // Also save as file for local development (if not Railway)
      if (!isRailway) {
        await QRCode.toFile("qr.png", qr, {
          width: 300,
          margin: 2,
          errorCorrectionLevel: "M",
        });
      }

      const serverUrl = isRailway
        ? `https://${process.env.RAILWAY_STATIC_URL || 'your-app.railway.app'}`
        : `http://localhost:${PORT}`;

      console.log("🖼️ REAL WhatsApp QR code generated successfully!");
      console.log(`🌐 Visit ${serverUrl} to scan the QR code`);
      console.log("⏰ QR code will expire in ~20 seconds and regenerate");
      console.log("📱 This QR code WILL connect to WhatsApp when scanned!");

      // Log QR code availability for debugging
      console.log("🔍 QR code stored in memory:", !!currentQRCode);
      console.log("🔍 QR code base64 length:", currentQRCode ? currentQRCode.length : 0);
    } catch (err) {
      console.error("❌ Failed to generate QR code:", err);
    }
  });

  client.on("loading_screen", (percent, message) => {
    console.log("⏳ Loading WhatsApp Web:", percent + "%", message);
  });

  client.once("ready", () => {
    console.log("✅ WhatsApp bot is ready and connected!");
    clientReady = true;
    currentQRCode = null; // Clear QR code when ready

    // Set up message handler ONLY when client is ready
    console.log("🔧 Setting up message handler for REAL WhatsApp messages...");
    setupMessageHandler();
  });

  client.on("disconnected", (reason) => {
    console.log("❌ WhatsApp client disconnected:", reason);
    clientReady = false;
    currentQRCode = null;
  });

  client.on("auth_failure", (msg) => {
    console.error("❌ Authentication failed:", msg);
    clientReady = false;
    currentQRCode = null;
  });

  client.on("authenticated", () => {
    console.log("🔐 WhatsApp client authenticated successfully!");
  });
} else {
  console.log("⚠️ No WhatsApp client to set up event handlers for");
}



// --- Google Sheets Config ---
const spreadsheetId = process.env.SPREADSHEET_ID;

function getIndonesiaDateTime() {
  const date = new Date();
  const options = {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  const formatter = new Intl.DateTimeFormat("en-GB", options);
  const parts = formatter.formatToParts(date);

  const dateParts = {};
  for (const { type, value } of parts) {
    if (type !== "literal") {
      dateParts[type] = value;
    }
  }

  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
}

// --- Real-Time RAG from Google Sheets ---
async function retrieveContext(prompt) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  const range = "RAG!A2:B";
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });

  const rows = response.data.values || [];
  const promptLower = prompt.toLowerCase();
  const matchedAnswers = [];

  for (const [keyword, answer] of rows) {
    const keywordLower = keyword.toLowerCase();
    if (promptLower.includes(keywordLower)) {
      matchedAnswers.push(`• ${answer}`);
    }
  }

  return matchedAnswers.length > 0
    ? matchedAnswers.join("\n\n")
    : "Maaf, saya hanya dapat membantu terkait informasi BPS Kota Pontianak. Silakan hubungi kontak resmi kami.";
}

// --- Append to Google Sheets (MESSAGE Sheet) ---
async function logMessageToSheet(sender, message, response) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: "MESSAGE!A2:D",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: [[sender, message, response, getIndonesiaDateTime()]],
    },
  });
}

// --- Message Handler Setup Function ---
function setupMessageHandler() {
  if (!client) {
    console.error("❌ Cannot setup message handler: client not available");
    return;
  }

  console.log("🔧 Setting up message handler for incoming WhatsApp messages...");

  // Handle incoming messages
  client.on("message_create", async (message) => {
    try {
      console.log("📨 Message received:", {
        from: message.from,
        body: message.body?.substring(0, 50) + "...",
        fromMe: message.fromMe,
        type: message.type
      });

      // Skip messages from bot itself
      if (message.fromMe) {
        console.log("⏭️ Skipping message from bot itself");
        return;
      }

      // Get message content
      const prompt = message.body?.toString().trim();
      if (!prompt) {
        console.log("⏭️ Skipping empty message");
        return;
      }

      console.log("🔍 Processing message:", prompt);
      const sender = message.from;

      // Get context from Google Sheets
      console.log("📊 Retrieving context from Google Sheets...");
      const context = await retrieveContext(prompt);
      console.log("✅ Context retrieved:", context.substring(0, 100) + "...");

      // Generate AI response
      console.log("🤖 Generating AI response with Gemini...");
      const result = await model.generateContent(
        `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${prompt}`
      );
      const response = result.response.text();
      console.log("✅ AI response generated:", response.substring(0, 100) + "...");

      // Send response
      console.log("📤 Sending response to WhatsApp...");
      await client.sendMessage(message.from, response);
      console.log("✅ Response sent successfully!");

      // Log to Google Sheets
      console.log("📝 Logging conversation to Google Sheets...");
      await logMessageToSheet(sender, prompt, response);
      console.log("✅ Conversation logged successfully!");

      console.log("🎉 Message processing completed successfully!");

    } catch (err) {
      console.error("❌ Error processing message:", err);
      console.error("❌ Error details:", {
        name: err.name,
        message: err.message,
        stack: err.stack?.substring(0, 200)
      });

      try {
        await client.sendMessage(
          message.from,
          "⚠️ Maaf, terjadi kesalahan. Silakan coba lagi nanti."
        );
        console.log("✅ Error message sent to user");
      } catch (sendError) {
        console.error("❌ Failed to send error message:", sendError);
      }
    }
  });

  console.log("✅ Message handler setup completed!");
}



// API Routes
app.get("/api/status", (req, res) => {
  res.json({
    environment: isRailway ? "railway" : (isDevelopment ? "development" : "production"),
    platform: isRailway ? "Railway" : "Local",
    clientReady: clientReady,
    hasQRCode: !!currentQRCode,
    realWhatsApp: true,
    message: clientReady ? "✅ WhatsApp bot connected and ready!" : "🔄 Connecting to WhatsApp..."
  });
});

app.get("/api/qr", (req, res) => {
  console.log("🔍 QR API called - currentQRCode exists:", !!currentQRCode);
  console.log("🔍 QR API called - clientReady:", clientReady);

  if (currentQRCode) {
    console.log("✅ Serving QR code to web interface");
    res.json({
      qrCode: currentQRCode,
      isReal: true,
      message: "✅ REAL WhatsApp QR Code - Scan with WhatsApp to connect!",
      timestamp: new Date().toISOString()
    });
  } else {
    console.log("❌ No QR code available for web interface");
    res.status(404).json({
      error: "No QR code available",
      message: "⏳ Waiting for WhatsApp QR code generation...",
      clientReady: clientReady,
      timestamp: new Date().toISOString()
    });
  }
});

// Debug endpoint
app.get("/api/debug", (req, res) => {
  res.json({
    environment: isRailway ? "railway" : (isDevelopment ? "development" : "production"),
    platform: isRailway ? "Railway" : "Local",
    clientReady: clientReady,
    hasQRCode: !!currentQRCode,
    qrCodeLength: currentQRCode ? currentQRCode.length : 0,
    nodeEnv: process.env.NODE_ENV,
    railwayEnv: process.env.RAILWAY_ENVIRONMENT_NAME,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    clientExists: !!client,
    isDevelopment: isDevelopment,
    isRailway: isRailway
  });
});

// Force initialization endpoint for debugging
app.post("/api/init", async (req, res) => {
  console.log("🔄 Force initialization requested via API");

  if (!client) {
    console.log("❌ No client available - attempting to recreate...");

    // Try to recreate client if it doesn't exist
    if (isRailway || isDevelopment) {
      try {
        const { Client, LocalAuth } = require("whatsapp-web.js");

        // Clean up any existing lock files before creating client
        const fs = require('fs');
        const path = require('path');
        const authPath = isRailway ? "/app/.wwebjs_auth" : "./.wwebjs_auth";
        const lockFile = path.join(authPath, 'session', 'SingletonLock');

        try {
          if (fs.existsSync(lockFile)) {
            console.log("🧹 Cleaning up existing Chrome lock file...");
            fs.unlinkSync(lockFile);
            console.log("✅ Lock file removed successfully");
          }
        } catch (error) {
          console.log("⚠️ Could not remove lock file:", error.message);
        }

        client = new Client({
          authStrategy: new LocalAuth({
            dataPath: authPath,
          }),
          puppeteer: {
            headless: true,
            executablePath: isRailway ? '/usr/bin/chromium-browser' : undefined,
            args: [
              "--no-sandbox",
              "--disable-setuid-sandbox",
              "--disable-dev-shm-usage",
              "--disable-gpu",
              "--disable-extensions",
              "--disable-web-security",
              "--disable-features=VizDisplayCompositor",
              // Fix for Chrome singleton lock issues
              "--disable-background-timer-throttling",
              "--disable-backgrounding-occluded-windows",
              "--disable-renderer-backgrounding",
              "--disable-features=TranslateUI",
              "--disable-ipc-flooding-protection",
              "--no-first-run",
              "--no-default-browser-check",
              "--disable-default-apps",
              "--disable-sync",
              "--disable-background-networking",
              "--disable-software-rasterizer",
              "--single-process"
            ],
          },
        });

        console.log("✅ Client recreated successfully");
      } catch (error) {
        console.error("❌ Failed to recreate client:", error);
        return res.status(500).json({
          error: "Failed to recreate WhatsApp client",
          message: error.message
        });
      }
    } else {
      return res.status(500).json({
        error: "No WhatsApp client available",
        message: "Client was not initialized and environment doesn't support recreation"
      });
    }
  }

  try {
    console.log("🔄 Attempting to initialize WhatsApp client...");
    console.log("🔧 Client state before init:", !!client);
    console.log("🔧 Environment:", { isRailway, isDevelopment });

    await client.initialize();

    console.log("✅ WhatsApp client initialization started successfully");
    res.json({
      success: true,
      message: "WhatsApp client initialization started",
      timestamp: new Date().toISOString(),
      clientExists: !!client,
      environment: isRailway ? "railway" : "development"
    });
  } catch (error) {
    console.error("❌ Force initialization failed:", error);
    console.log("🔧 Error details:", {
      name: error.name,
      message: error.message,
      stack: error.stack?.substring(0, 200)
    });

    res.status(500).json({
      error: error.message,
      message: "Failed to initialize WhatsApp client",
      details: {
        errorName: error.name,
        clientExists: !!client,
        environment: isRailway ? "railway" : "development"
      }
    });
  }
});

// Cleanup endpoint for Chrome lock files
app.post("/api/cleanup", (req, res) => {
  console.log("🧹 Manual cleanup requested via API");

  const fs = require('fs');
  const path = require('path');
  const authPath = isRailway ? "/app/.wwebjs_auth" : "./.wwebjs_auth";
  const lockFile = path.join(authPath, 'session', 'SingletonLock');

  try {
    if (fs.existsSync(lockFile)) {
      fs.unlinkSync(lockFile);
      console.log("✅ Manual cleanup: Lock file removed successfully");
      res.json({
        success: true,
        message: "Chrome lock file removed successfully",
        lockFile: lockFile
      });
    } else {
      console.log("ℹ️ Manual cleanup: No lock file found");
      res.json({
        success: true,
        message: "No lock file found to remove",
        lockFile: lockFile
      });
    }
  } catch (error) {
    console.error("❌ Manual cleanup failed:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Failed to remove lock file"
    });
  }
});

// Test message endpoint for debugging chatbot
app.post("/api/test-message", async (req, res) => {
  console.log("🧪 Test message endpoint called");

  if (!clientReady) {
    return res.status(400).json({
      error: "WhatsApp client not ready",
      message: "Please wait for WhatsApp client to connect first"
    });
  }

  const { message: testMessage } = req.body;
  if (!testMessage) {
    return res.status(400).json({
      error: "No message provided",
      message: "Please provide a 'message' field in the request body"
    });
  }

  try {
    console.log("🧪 Testing message processing:", testMessage);

    // Test context retrieval
    const context = await retrieveContext(testMessage);
    console.log("✅ Context retrieved for test:", context.substring(0, 100) + "...");

    // Test AI response generation
    const result = await model.generateContent(
      `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${testMessage}`
    );
    const response = result.response.text();
    console.log("✅ AI response generated for test:", response.substring(0, 100) + "...");

    res.json({
      success: true,
      testMessage: testMessage,
      context: context,
      response: response,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("❌ Test message failed:", error);
    res.status(500).json({
      error: error.message,
      message: "Test message processing failed"
    });
  }
});

// Main route - QR Code Interface
app.get("/", (req, res) => {
  res.send(`
    <html>
      <head>
        <title>WhatsApp Chatbot - Scan QR Code</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 500px;
            width: 100%;
          }
          #qrcode {
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            background: white;
            border-radius: 15px;
            margin: 20px 0;
            max-width: 300px;
            height: auto;
          }
          .status {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            font-weight: bold;
          }
          .loading { background: rgba(255, 193, 7, 0.3); }
          .ready { background: rgba(76, 175, 80, 0.3); }
          .error { background: rgba(244, 67, 54, 0.3); }
          button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
          }
          button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
          }
          .qr-placeholder {
            background: white;
            color: #333;
            padding: 40px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>🤖 WhatsApp Chatbot BPS Pontianak</h1>
          <h2>� Railway Deployment - REAL WhatsApp Bot</h2>

          <div class="status ready" style="background: rgba(76, 175, 80, 0.3);">
            ✅ Running on Railway - REAL WhatsApp QR Codes!
          </div>

          <div class="info-box" style="background: rgba(33, 150, 243, 0.1); border: 1px solid rgba(33, 150, 243, 0.3); border-radius: 8px; padding: 15px; margin: 20px 0; font-size: 14px;">
            <strong>� Railway Deployment Info:</strong><br>
            • QR code generation takes 30-60 seconds on first load<br>
            • WhatsApp client needs to connect to servers<br>
            • If QR doesn't appear, check Railway logs for ASCII version<br>
            • QR codes expire every ~20 seconds and auto-refresh
          </div>

          <div id="status" class="status loading">
            🔄 Initializing WhatsApp client on Railway...
          </div>

          <div id="qr-container">
            <img id="qrcode" alt="Real WhatsApp QR Code" style="display: none;" />
            <div id="qr-placeholder" class="qr-placeholder">
              <p>⏳ Waiting for REAL WhatsApp QR code...</p>
              <p>Connecting to WhatsApp servers...</p>
            </div>
          </div>

          <button onclick="refreshQR()">🔄 Get New QR Code</button>
          <button onclick="checkStatus()">📊 Check Connection</button>
          <button onclick="forceInit()" style="background: rgba(255, 152, 0, 0.3);">⚡ Force Initialize</button>
          <button onclick="cleanupLocks()" style="background: rgba(244, 67, 54, 0.3);">🧹 Cleanup Locks</button>
          <button onclick="testChatbot()" style="background: rgba(76, 175, 80, 0.3);">🧪 Test Chatbot</button>

          <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <div id="qr-instructions">
              <p>📋 How to Connect:</p>
              <ol style="text-align: left; max-width: 300px;">
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the REAL QR code above</li>
                <li>Your phone will connect to this bot!</li>
              </ol>
            </div>

            <div style="background: rgba(76, 175, 80, 0.2); padding: 15px; border-radius: 10px; margin-top: 15px;">
              <p>✅ <strong>Real WhatsApp Connection</strong></p>
              <p style="font-size: 12px;">This generates REAL WhatsApp QR codes that will actually connect your phone to this chatbot. No demo codes!</p>
            </div>
          </div>
        </div>

        <script>
          let checkInterval;

          let qrAttempts = 0;
          const maxQRAttempts = 20; // Try for 20 attempts (1 minute)

          function checkStatus() {
            fetch('/api/status')
              .then(response => response.json())
              .then(data => {
                console.log('Status response:', data);
                const statusDiv = document.getElementById('status');
                if (data.clientReady) {
                  statusDiv.className = 'status ready';
                  statusDiv.innerHTML = '✅ WhatsApp bot connected! You can now chat with the bot.';
                  document.getElementById('qr-container').style.display = 'none';
                  clearInterval(checkInterval);
                } else if (data.hasQRCode) {
                  statusDiv.className = 'status loading';
                  statusDiv.innerHTML = '📱 REAL WhatsApp QR code ready - scan now to connect!';
                  loadQRCode();
                  qrAttempts = 0; // Reset attempts when QR is available
                } else {
                  qrAttempts++;
                  statusDiv.className = 'status loading';
                  if (qrAttempts <= 5) {
                    statusDiv.innerHTML = '🔄 Starting WhatsApp client... (' + qrAttempts + '/5)';
                  } else if (qrAttempts <= 15) {
                    statusDiv.innerHTML = '⏳ Connecting to WhatsApp servers... (' + qrAttempts + '/15)';
                  } else if (qrAttempts <= maxQRAttempts) {
                    statusDiv.innerHTML = '🔄 Waiting for QR code generation... (' + qrAttempts + '/' + maxQRAttempts + ')';
                  } else {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '⚠️ QR code taking longer than expected. Check Railway logs for ASCII QR code.';
                  }

                  // Try to load QR code anyway in case status is delayed
                  if (qrAttempts > 5) {
                    loadQRCode();
                  }
                }
              })
              .catch(error => {
                console.error('Status error:', error);
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Connection error - trying to load QR code directly';
                loadQRCode();
              });
          }

          function loadQRCode() {
            console.log('Attempting to load QR code... (attempt ' + (qrAttempts || 0) + ')');
            fetch('/api/qr')
              .then(response => {
                console.log('QR API response status:', response.status);
                if (response.status === 404) {
                  // 404 is expected when QR code isn't ready yet
                  return response.json().then(data => {
                    console.log('QR not ready:', data.message);
                    showQRWaiting(data.message || 'QR code not ready yet');
                    return null;
                  });
                } else if (!response.ok) {
                  throw new Error('QR API returned ' + response.status);
                }
                return response.json();
              })
              .then(data => {
                if (!data) return; // 404 case handled above

                console.log('QR API response data:', data);
                if (data.qrCode) {
                  const qrImg = document.getElementById('qrcode');
                  const placeholder = document.getElementById('qr-placeholder');

                  qrImg.src = data.qrCode;
                  qrImg.style.display = 'block';
                  placeholder.style.display = 'none';

                  // Update status to indicate this is a real QR code
                  document.getElementById('status').className = 'status loading';
                  document.getElementById('status').innerHTML = '📱 REAL WhatsApp QR code active - scan NOW to connect! (expires in ~20 seconds)';

                  console.log('✅ QR code loaded successfully');
                  qrAttempts = 0; // Reset attempts on success

                  // Show expiration warning after 18 seconds
                  setTimeout(() => {
                    if (document.getElementById('status').innerHTML.includes('scan NOW')) {
                      document.getElementById('status').innerHTML = '⏰ QR code expired - refreshing...';
                      document.getElementById('status').className = 'status loading';
                      // Auto-refresh after expiration
                      setTimeout(checkStatus, 2000);
                    }
                  }, 18000);
                } else {
                  console.log('No QR code in response');
                  showQRWaiting('QR code not available in response');
                }
              })
              .catch(error => {
                console.error('QR Code API error:', error);
                if (error.message.includes('404')) {
                  showQRWaiting('QR code not ready yet - still connecting to WhatsApp servers');
                } else {
                  showQRError('QR API error: ' + error.message);
                }
              });
          }

          function showQRWaiting(message) {
            console.log('QR Waiting:', message);
            const placeholder = document.getElementById('qr-placeholder');
            const qrImg = document.getElementById('qrcode');

            qrImg.style.display = 'none';
            placeholder.style.display = 'block';
            placeholder.innerHTML =
              '<p>⏳ Connecting to WhatsApp...</p>' +
              '<p style="font-size: 14px;">' + message + '</p>' +
              '<p style="font-size: 12px; opacity: 0.7;">This can take 30-60 seconds on Railway</p>';
          }

          function showQRError(message) {
            console.log('QR Error:', message);
            const placeholder = document.getElementById('qr-placeholder');
            const qrImg = document.getElementById('qrcode');

            qrImg.style.display = 'none';
            placeholder.style.display = 'block';
            placeholder.innerHTML =
              '<p>⚠️ QR code issue</p>' +
              '<p style="font-size: 14px; color: #ff6b6b;">' + message + '</p>' +
              '<p style="font-size: 12px; opacity: 0.7;">Check Railway logs for ASCII QR code</p>' +
              '<button onclick="refreshQR()" style="margin-top: 10px; padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 Try Again</button>';
          }

          function loadQRCodeFallback() {
            // For Railway, we don't use file fallback since filesystem is ephemeral
            // Just show waiting message
            showQRWaiting('Checking for QR code availability...');
          }

          function refreshQR() {
            console.log('Refreshing QR code...');
            document.getElementById('qrcode').style.display = 'none';
            document.getElementById('qr-placeholder').style.display = 'block';
            document.getElementById('qr-placeholder').innerHTML =
              '<p>⏳ Getting new REAL WhatsApp QR code...</p><p>Connecting to WhatsApp servers...</p>';

            // Force a status check and QR load
            setTimeout(() => {
              checkStatus();
            }, 1000);
          }

          function forceInit() {
            console.log('Force initializing WhatsApp client...');
            document.getElementById('status').className = 'status loading';
            document.getElementById('status').innerHTML = '⚡ Force initializing WhatsApp client...';

            fetch('/api/init', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(response => response.json())
            .then(data => {
              console.log('Force init response:', data);
              if (data.success) {
                document.getElementById('status').className = 'status loading';
                document.getElementById('status').innerHTML = '✅ Force initialization started! Waiting for QR code...';
                qrAttempts = 0; // Reset attempts
                setTimeout(checkStatus, 2000);
              } else {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Force init failed: ' + (data.error || 'Unknown error');
              }
            })
            .catch(error => {
              console.error('Force init error:', error);
              document.getElementById('status').className = 'status error';
              document.getElementById('status').innerHTML = '❌ Force init request failed: ' + error.message;
            });
          }

          function cleanupLocks() {
            console.log('Cleaning up Chrome lock files...');
            document.getElementById('status').className = 'status loading';
            document.getElementById('status').innerHTML = '🧹 Cleaning up Chrome lock files...';

            fetch('/api/cleanup', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              }
            })
            .then(response => response.json())
            .then(data => {
              console.log('Cleanup response:', data);
              if (data.success) {
                document.getElementById('status').className = 'status loading';
                document.getElementById('status').innerHTML = '✅ Cleanup completed! Try force initialize now.';
                setTimeout(() => {
                  document.getElementById('status').innerHTML = '🔄 Ready for force initialization...';
                }, 2000);
              } else {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Cleanup failed: ' + (data.error || 'Unknown error');
              }
            })
            .catch(error => {
              console.error('Cleanup error:', error);
              document.getElementById('status').className = 'status error';
              document.getElementById('status').innerHTML = '❌ Cleanup request failed: ' + error.message;
            });
          }

          function testChatbot() {
            console.log('Testing chatbot functionality...');
            document.getElementById('status').className = 'status loading';
            document.getElementById('status').innerHTML = '🧪 Testing chatbot functionality...';

            const testMessage = 'Apa itu BPS?';

            fetch('/api/test-message', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                message: testMessage
              })
            })
            .then(response => response.json())
            .then(data => {
              console.log('Test response:', data);
              if (data.success) {
                document.getElementById('status').className = 'status ready';
                document.getElementById('status').innerHTML =
                  '✅ Chatbot test successful!<br>' +
                  '<small>Test: "' + testMessage + '"<br>' +
                  'Response: "' + data.response.substring(0, 100) + '..."</small>';
              } else {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').innerHTML = '❌ Chatbot test failed: ' + (data.error || 'Unknown error');
              }
            })
            .catch(error => {
              console.error('Test error:', error);
              document.getElementById('status').className = 'status error';
              document.getElementById('status').innerHTML = '❌ Chatbot test request failed: ' + error.message;
            });
          }

          // Auto-refresh status every 3 seconds (more frequent for QR code detection)
          checkInterval = setInterval(checkStatus, 3000);

          // Initial load - start immediately
          checkStatus();
        </script>
      </body>
    </html>
  `);
});

// NO DEMO QR CODES - ONLY REAL WHATSAPP QR CODES

// --- Start Express Server - REAL WHATSAPP ONLY ---
app.listen(PORT, async () => {
  const serverUrl = isRailway
    ? `https://${process.env.RAILWAY_STATIC_URL || 'your-app.railway.app'}`
    : `http://localhost:${PORT}`;

  console.log(`🌐 Server running at: ${serverUrl}`);
  console.log(`🚀 Platform: ${isRailway ? 'Railway (Production)' : 'Local Development'}`);
  console.log(`📱 REAL WhatsApp QR Codes ONLY - No Demo Codes!`);

  if (client && (isDevelopment || isRailway)) {
    try {
      console.log("🔄 Starting WhatsApp client initialization...");
      console.log("🔧 Client exists:", !!client);
      console.log("🔧 Environment check - isDevelopment:", isDevelopment);
      console.log("🔧 Environment check - isRailway:", isRailway);

      await client.initialize();
      console.log("🔄 REAL WhatsApp client initialization started...");
      console.log("⏳ Waiting for REAL WhatsApp QR code from WhatsApp servers...");
      console.log(`🌐 Visit ${serverUrl} to scan the QR code!`);
      console.log("📋 If QR code doesn't appear, check Railway logs for the ASCII QR code");
    } catch (error) {
      console.error("❌ CRITICAL: Failed to initialize WhatsApp client:", error);
      console.log("❌ Cannot generate REAL QR codes!");
      console.log("📝 Please ensure WhatsApp Web.js is properly installed");
      console.log("🔧 Debug info:");
      console.log("  - Client exists:", !!client);
      console.log("  - isDevelopment:", isDevelopment);
      console.log("  - isRailway:", isRailway);
      console.log("  - NODE_ENV:", process.env.NODE_ENV);
      console.log("  - RAILWAY_ENVIRONMENT_NAME:", process.env.RAILWAY_ENVIRONMENT_NAME);

      // Don't exit on Railway, just log the error
      if (!isRailway) {
        process.exit(1);
      }
    }
  } else {
    console.log("⚠️ WhatsApp client not initialized");
    console.log("🔧 Debug info:");
    console.log("  - Client exists:", !!client);
    console.log("  - isDevelopment:", isDevelopment);
    console.log("  - isRailway:", isRailway);
    console.log("  - NODE_ENV:", process.env.NODE_ENV);
    console.log("  - RAILWAY_ENVIRONMENT_NAME:", process.env.RAILWAY_ENVIRONMENT_NAME);
  }
});
