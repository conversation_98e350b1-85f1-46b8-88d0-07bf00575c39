const fs = require("fs");
const puppeteer = require("puppeteer"); // Import full Puppeteer
const QRCode = require("qrcode");
const { Client, LocalAuth } = require("whatsapp-web.js");
const qrcode = require("qrcode-terminal");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const { google } = require("googleapis");
require("dotenv").config();

// --- Gemini AI Init ---
const genAI = new GoogleGenerativeAI(process.env.API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

// --- WhatsApp Client ---
const client = new Client({
  authStrategy: new LocalAuth(), // Keep sessions persistent
  puppeteer: {
    executablePath: puppeteer.executablePath(), // Explicitly use installed Puppeteer
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
    headless: true,
  },
});

client.on("qr", async (qr) => {
  console.log("📱 Scan this QR code with WhatsApp:");
  qrcode.generate(qr, { small: true });

  try {
    await QRCode.toFile("qr.png", qr); // Save QR code as image
    console.log("🖼️ QR code saved as qr.png – open it to scan.");
  } catch (err) {
    console.error("❌ Failed to save QR code:", err);
  }
});

client.once("ready", () => {
  console.log("✅ WhatsApp bot is ready!");
});

const spreadsheetId = process.env.SPREADSHEET_ID;

function getIndonesiaDateTime() {
  const date = new Date();

  const options = {
    timeZone: "Asia/Jakarta",
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  };

  const formatter = new Intl.DateTimeFormat("en-GB", options);
  const parts = formatter.formatToParts(date);

  const dateParts = {};
  for (const { type, value } of parts) {
    if (type !== "literal") {
      dateParts[type] = value;
    }
  }

  return `${dateParts.year}-${dateParts.month}-${dateParts.day} ${dateParts.hour}:${dateParts.minute}:${dateParts.second}`;
}

// --- Real-Time RAG from Google Sheets ---
async function retrieveContext(prompt) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets.readonly"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  const range = "RAG!A2:B";

  const response = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range,
  });
  const rows = response.data.values || [];

  const promptLower = prompt.toLowerCase();
  const matchedAnswers = [];

  for (const [keyword, answer] of rows) {
    const keywordLower = keyword.toLowerCase();
    if (promptLower.includes(keywordLower)) {
      matchedAnswers.push(`• ${answer}`);
    }
  }

  if (matchedAnswers.length > 0) {
    return matchedAnswers.join("\n\n");
  }

  return "Maaf, saya hanya dapat membantu terkait informasi BPS Kota Pontianak. Silakan hubungi kontak resmi kami.";
}

// --- Append to Google Sheets (MESSAGE Sheet) ---
async function logMessageToSheet(sender, message, response) {
  const auth = new google.auth.GoogleAuth({
    keyFile: "./credentials.json",
    scopes: ["https://www.googleapis.com/auth/spreadsheets"],
  });

  const sheets = google.sheets({ version: "v4", auth: await auth.getClient() });

  await sheets.spreadsheets.values.append({
    spreadsheetId,
    range: "MESSAGE!A2:D",
    valueInputOption: "USER_ENTERED",
    requestBody: {
      values: [[sender, message, response, getIndonesiaDateTime()]],
    },
  });
}

// --- Message Handler ---
client.on("message_create", async (message) => {
  try {
    if (message.fromMe) return;

    const prompt = message.body.toString().trim();
    if (!prompt) return;

    const sender = message.from;

    const context = await retrieveContext(prompt);

    const result = await model.generateContent(
      `Sebagai admin WhatsApp BPS Kota Pontianak, jawab pertanyaan ini berdasarkan konteks berikut:\n\n${context}\n\nPertanyaan:\n${prompt}`
    );
    const response = result.response.text();

    console.log("🟢 Replied to:", prompt);
    await client.sendMessage(message.from, response);
    console.log("🟢 Replied with:", response);

    await logMessageToSheet(sender, prompt, response);
  } catch (err) {
    console.error("❌ Error:", err);
    await client.sendMessage(
      message.from,
      "⚠️ Maaf, terjadi kesalahan. Silakan coba lagi nanti."
    );
  }
});

// --- Start WhatsApp Bot ---
client.initialize();
