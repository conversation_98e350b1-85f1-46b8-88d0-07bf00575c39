#!/bin/bash

# 🚀 WhatsApp Chatbot VPS Setup Script
# Run this on a fresh Ubuntu 22.04 VPS

set -e

echo "🚀 Starting WhatsApp Chatbot VPS Setup..."
echo "================================================"

# Update system
echo "📦 Updating system packages..."
apt update && apt upgrade -y

# Install Node.js 18
echo "📦 Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install PM2
echo "📦 Installing PM2 process manager..."
npm install -g pm2

# Install Git if not present
echo "📦 Installing Git..."
apt-get install -y git

# Install dependencies for Puppeteer
echo "📦 Installing Puppeteer dependencies..."
apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    wget \
    xdg-utils

# Setup firewall
echo "🔒 Setting up firewall..."
ufw allow ssh
ufw allow 3002
ufw --force enable

# Create app directory
echo "📁 Creating application directory..."
mkdir -p /opt/whatsapp-bot
cd /opt/whatsapp-bot

# Clone repository (you'll need to update this URL)
echo "📥 Cloning repository..."
echo "⚠️  Please update the repository URL in this script!"
# git clone https://github.com/your-username/whatsapp-chatbot-bps-6171.git .

# For now, create a placeholder
echo "Please manually clone your repository to /opt/whatsapp-bot"
echo "git clone https://github.com/your-username/whatsapp-chatbot-bps-6171.git /opt/whatsapp-bot"

# Install dependencies (uncomment after cloning)
# echo "📦 Installing Node.js dependencies..."
# npm install

# Create environment file template
echo "📝 Creating environment file template..."
cat > .env.example << EOF
# WhatsApp Chatbot Environment Variables
API_KEY=your_gemini_api_key_here
SPREADSHEET_ID=your_google_sheets_id_here
PORT=3002
NODE_ENV=production

# Google Sheets Credentials
# Place your credentials.json file in the project root
EOF

# Create PM2 ecosystem file
echo "📝 Creating PM2 ecosystem file..."
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'whatsapp-bot',
    script: 'index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Create logs directory
mkdir -p logs

# Create startup script
echo "📝 Creating startup script..."
cat > start-bot.sh << EOF
#!/bin/bash
cd /opt/whatsapp-bot
pm2 start ecosystem.config.js
pm2 save
EOF

chmod +x start-bot.sh

# Create stop script
cat > stop-bot.sh << EOF
#!/bin/bash
pm2 stop whatsapp-bot
EOF

chmod +x stop-bot.sh

# Create restart script
cat > restart-bot.sh << EOF
#!/bin/bash
cd /opt/whatsapp-bot
pm2 restart whatsapp-bot
EOF

chmod +x restart-bot.sh

# Setup PM2 startup
echo "🔄 Setting up PM2 startup..."
pm2 startup systemd -u root --hp /root

echo "✅ VPS Setup Complete!"
echo "================================================"
echo ""
echo "📋 Next Steps:"
echo "1. Clone your repository:"
echo "   git clone https://github.com/your-username/whatsapp-chatbot-bps-6171.git /opt/whatsapp-bot"
echo ""
echo "2. Install dependencies:"
echo "   cd /opt/whatsapp-bot && npm install"
echo ""
echo "3. Set up environment variables:"
echo "   cp .env.example .env"
echo "   nano .env  # Add your API keys"
echo ""
echo "4. Add your Google Sheets credentials:"
echo "   # Upload credentials.json to /opt/whatsapp-bot/"
echo ""
echo "5. Start the bot:"
echo "   ./start-bot.sh"
echo ""
echo "6. Check status:"
echo "   pm2 status"
echo "   pm2 logs whatsapp-bot"
echo ""
echo "7. Access your bot:"
echo "   http://your-server-ip:3002"
echo ""
echo "🔧 Management Commands:"
echo "   ./start-bot.sh    # Start the bot"
echo "   ./stop-bot.sh     # Stop the bot"
echo "   ./restart-bot.sh  # Restart the bot"
echo "   pm2 logs whatsapp-bot  # View logs"
echo ""
echo "🎉 Your WhatsApp bot will generate REAL QR codes!"
