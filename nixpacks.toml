[phases.setup]
nixPkgs = ['nodejs_18', 'npm', 'chromium', 'nss', 'freetype', 'freetype-dev', 'harfbuzz', 'ca-certificates', 'ttf-freefont']

[phases.install]
cmds = [
  'npm ci --only=production',
  'npx puppeteer browsers install chrome'
]

[phases.build]
cmds = ['echo "Build completed"']

[start]
cmd = 'npm start'

[variables]
NODE_ENV = 'production'
PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = 'true'
PUPPETEER_EXECUTABLE_PATH = '/nix/store/*-chromium-*/bin/chromium'
