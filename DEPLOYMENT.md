# Deployment Guide

## 🚀 Quick Start

### Local Development (Recommended)

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Run the bot**
   ```bash
   npm run dev
   ```

4. **Access the interface**
   - Open http://localhost:3000
   - Scan QR code with WhatsApp
   - Start chatting!

### Netlify Deployment

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Deploy to Netlify"
   git push origin main
   ```

2. **Deploy to Netlify**
   - Connect your GitHub repository to Netlify
   - Set build command: `npm run build`
   - Set publish directory: `public`
   - Deploy!

## 🔧 Configuration

### Environment Variables

Create a `.env` file with:

```env
# Required
API_KEY=your_gemini_api_key
SPREADSHEET_ID=your_google_sheets_id

# Optional
NODE_ENV=development
PORT=3000
```

### Google Sheets Setup

1. Create a Google Sheets document
2. Add two sheets:
   - `RAG`: Keywords and responses
   - `MESSAGE`: Conversation logs
3. Enable Google Sheets API
4. Download `credentials.json`
5. Share sheet with service account email

### Netlify Environment Variables

In your Netlify dashboard, add:
- `API_KEY`: Your Gemini API key
- `SPREADSHEET_ID`: Your Google Sheets ID
- `NETLIFY`: `true`

## 📁 File Structure

```
project/
├── index.js              # Main application
├── package.json          # Dependencies
├── netlify.toml          # Netlify config
├── .env                  # Environment variables
├── credentials.json      # Google credentials
├── public/               # Static files
│   └── index.html       # Netlify landing page
└── netlify/
    └── functions/
        └── api.js       # Serverless functions
```

## 🔍 Testing

Run the test script to verify setup:

```bash
node test-local.js
```

## 🐛 Troubleshooting

### Common Issues

1. **QR Code not showing**
   - Check if WhatsApp Web is accessible
   - Verify Puppeteer installation
   - Try refreshing the page

2. **Google Sheets errors**
   - Verify credentials.json is present
   - Check spreadsheet permissions
   - Ensure API is enabled

3. **Netlify deployment issues**
   - Check build logs
   - Verify environment variables
   - Ensure all files are committed

### Debug Mode

Enable debug logging:

```bash
DEBUG=* npm run dev
```

## 🔒 Security

- Never commit `.env` or `credentials.json`
- Use environment variables for sensitive data
- Regularly rotate API keys
- Monitor usage and logs

## 📞 Support

For technical issues:
1. Check the troubleshooting section
2. Review console logs
3. Create an issue on GitHub

For BPS Pontianak services:
- Contact official BPS channels
