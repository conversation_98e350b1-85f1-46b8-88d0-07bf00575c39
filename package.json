{"name": "whatsappchatbot", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "set NODE_ENV=development && nodemon index.js", "start": "node index.js", "build": "echo 'No build step required'", "railway:start": "NODE_ENV=production node index.js"}, "author": "", "license": "ISC", "description": "WhatsApp <PERSON>bot for BPS Pontianak with Railway deployment support", "dependencies": {"@google/genai": "^1.0.1", "@google/generative-ai": "^0.17.2", "dotenv": "^16.5.0", "express": "^4.21.2", "fs": "^0.0.1-security", "googleapis": "^149.0.0", "qrcode": "^1.5.4", "whatsapp-web.js": "^1.25.0", "puppeteer": "^21.0.0"}, "devDependencies": {"nodemon": "^3.1.4", "qrcode-terminal": "^0.12.0"}, "engines": {"node": ">=18.0.0"}}